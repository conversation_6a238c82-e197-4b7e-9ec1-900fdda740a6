import { Modality } from '../gemini/client.js';
import { endSession } from './session-utils.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import { websocketLogger } from '../utils/logger.js';

/**
 * Common Gemini session creation for both Twilio and local testing
 */
export async function createCommonGeminiSession(sessionId, sessionConfig, deps, connectionData, ws, flowType, sessionType) {
    const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
    const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;

    let geminiSession = null;
    let isSessionActive = false;

    try {
        geminiSession = await deps.sessionManager.geminiClient.live.connect({
            model: correctModel,
            callbacks: {
                onopen: () => {
                    isSessionActive = true;
                    // Update connection data with active status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = true;
                    }
                },
                onmessage: (message) => handleCommonGeminiMessage(sessionId, message, deps, connectionData, sessionType),
                onerror: () => {
                    isSessionActive = false;
                    // Update connection data with inactive status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = false;
                    }
                },
                onclose: () => {
                    isSessionActive = false;
                    // Update connection data with inactive status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = false;
                    }
                }
            },
            config: {
                responseModalities: [Modality.AUDIO],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: correctVoice
                        }
                    }
                }
            },
            temperature: 1.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192
        });

        // Start heartbeat monitoring
        const heartbeatInterval = sessionType === 'twilio_call' ? 60000 : 30000; // Longer for Twilio
        const heartbeatTimeout = sessionType === 'twilio_call' ? 30000 : 10000;
        
        globalHeartbeatManager.startHeartbeat(sessionId, ws, heartbeatInterval, heartbeatTimeout, () => {
            if (sessionType === 'twilio_call') {
                // For Twilio, don't immediately end session on heartbeat timeout
                websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid: sessionId });
                const connectionData = deps.activeConnections?.get(sessionId);
                if (connectionData) {
                    connectionData.heartbeatTimeout = true;
                    connectionData.lastHeartbeatTimeout = Date.now();
                }
            } else {
                // For local testing, end session on heartbeat timeout
                endSession(sessionId, { activeConnections: deps.activeConnections, lifecycleManager: deps.lifecycleManager }, 'heartbeat_timeout');
            }
        });

        // Send initial AI instructions
        if (sessionConfig.aiInstructions && geminiSession) {
            await sendInitialInstructions(sessionId, sessionConfig, geminiSession, flowType, sessionType);
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Failed to create Gemini session:`, error);
        geminiSession = null;
    }

    return { geminiSession, isSessionActive };
}

/**
 * Send initial AI instructions based on session type and flow
 */
async function sendInitialInstructions(sessionId, sessionConfig, geminiSession, flowType, sessionType) {
    try {
        let messageText = sessionConfig.aiInstructions;

        // Handle different flow types
        if (flowType === 'outbound_test' || flowType === 'outbound_call') {
            // Outbound: Add conversation trigger
            if (sessionType === 'local_test') {
                messageText = `${sessionConfig.aiInstructions}\n\nHello, call answered. Start speaking now.`;
            } else {
                messageText = `${sessionConfig.aiInstructions}\n\nThe call has been answered. Start the conversation immediately according to your instructions.`;
            }
        } else if (flowType === 'inbound_test' || flowType === 'inbound_call') {
            // Incoming: Provide default instructions if empty and add greeting trigger
            if (!messageText || messageText.trim() === '') {
                messageText = `You are a helpful customer service representative. You should greet the caller warmly and ask how you can help them today. Be professional, friendly, and helpful.`;
            }
            messageText = `${messageText}\n\nA customer has just called you. Greet them warmly and ask how you can help them today. Start speaking immediately.`;
        }

        await geminiSession.sendClientContent({
            turns: [{
                role: 'user',
                parts: [{
                    text: messageText
                }]
            }],
            turnComplete: true
        });

        console.log(`✅ [${sessionId}] Initial instructions sent for ${flowType}`);

    } catch (error) {
        console.error(`❌ [${sessionId}] Failed to send initial instructions:`, error);
    }
}

/**
 * Common Gemini message handler for both Twilio and local testing
 */
function handleCommonGeminiMessage(sessionId, message, deps, connectionData, sessionType) {
    console.log(`🔍 [${sessionId}] handleCommonGeminiMessage called - sessionType: ${sessionType}, message keys:`, Object.keys(message || {}));

    if (message.setupComplete || message.goAway) {
        console.log(`🔍 [${sessionId}] Skipping setupComplete or goAway message`);
        return;
    }

    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    const text = message.serverContent?.modelTurn?.parts?.[0]?.text;

    console.log(`🔍 [${sessionId}] Audio check: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

    // Handle audio responses
    if (audio && audio.data && audio.data.length > 0) {
        handleAudioResponse(sessionId, audio, deps, connectionData, sessionType);
    }

    // Handle text responses
    if (text) {
        console.log(`💬 [${sessionId}] Text response received: ${text.substring(0, 100)}...`);
        const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
        if (freshConnectionData) {
            freshConnectionData.lastAIResponse = Date.now();
            freshConnectionData.responseTimeouts = 0;
            freshConnectionData.connectionQuality = 'good';
        }
    }
}

/**
 * Handle audio responses from Gemini - route to appropriate destination
 */
function handleAudioResponse(sessionId, audio, deps, connectionData, sessionType) {
    // Get fresh connection data to ensure we have the latest WebSocket state
    const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
    const localWs = freshConnectionData?.localWs;
    const twilioWs = freshConnectionData?.twilioWs;

    console.log(`🔍 [${sessionId}] Audio forwarding check: audio=${!!audio}, data=${audio.data?.length || 0}, sessionType=${sessionType}, localWs=${!!localWs}, twilioWs=${!!twilioWs}`);

    if (sessionType === 'local_test') {
        // Handle local testing sessions
        handleLocalAudioResponse(sessionId, audio, localWs);
    } else if (sessionType === 'twilio_call') {
        // Handle Twilio sessions (inbound/outbound calls)
        handleTwilioAudioResponse(sessionId, audio, twilioWs, freshConnectionData, deps);
    } else {
        console.warn(`⚠️ [${sessionId}] Unknown session type: ${sessionType}`);
    }
}

/**
 * Handle audio response for local testing
 */
function handleLocalAudioResponse(sessionId, audio, localWs) {
    if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
        console.log(`🔊 [${sessionId}] Sending audio response to local client, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

        try {
            localWs.send(JSON.stringify({
                type: 'audio',
                audio: audio.data,
                mimeType: audio.mimeType
            }));
            console.log(`✅ [${sessionId}] Audio sent successfully to local WebSocket`);
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Error sending audio to local WebSocket:`, sendError);
        }
    } else {
        console.warn(`⚠️ [${sessionId}] Cannot send audio to local WebSocket: readyState=${localWs?.readyState}`);
    }
}

/**
 * Handle audio response for Twilio calls
 */
function handleTwilioAudioResponse(sessionId, audio, twilioWs, connectionData, deps) {
    if (twilioWs && twilioWs.readyState === 1) { // WebSocket.OPEN
        console.log(`🔊 [${sessionId}] Sending audio response to Twilio, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

        try {
            // Convert Gemini PCM audio to μ-law format for Twilio
            const convertedAudio = deps.audioProcessor?.fallbackPCMToUlaw(audio.data);
            if (convertedAudio) {
                // Initialize sequence number if not exists
                if (!connectionData.sequenceNumber) {
                    connectionData.sequenceNumber = 0;
                }

                const audioDelta = {
                    event: 'media',
                    sequenceNumber: connectionData.sequenceNumber.toString(),
                    streamSid: connectionData.streamSid,
                    media: {
                        payload: convertedAudio
                    }
                };

                twilioWs.send(JSON.stringify(audioDelta));
                connectionData.sequenceNumber++;
                console.log(`✅ [${sessionId}] Audio sent successfully to Twilio WebSocket`);
            } else {
                console.error(`❌ [${sessionId}] Failed to convert audio for Twilio`);
            }
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Error sending audio to Twilio WebSocket:`, sendError);
        }
    } else {
        console.warn(`⚠️ [${sessionId}] Cannot send audio to Twilio WebSocket: readyState=${twilioWs?.readyState}`);
    }
}

/**
 * Create connection data for local testing sessions
 */
export function createLocalConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall) {
    return {
        localWs: connection.socket || connection,
        sessionId,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'local_test',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Test Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTestMode: true,
        lastAIResponse: Date.now(),
        responseTimeouts: 0,
        connectionQuality: 'good',
        lastContextSave: Date.now(),
        contextSaveInterval: null
    };
}

/**
 * Create connection data for Twilio call sessions
 */
export function createTwilioConnectionData(callSid, ws, streamSid, sessionConfig, flowType, isIncomingCall) {
    return {
        twilioWs: ws,
        callSid,
        streamSid: streamSid,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'twilio_call',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTwilioCall: true,
        sequenceNumber: 0
    };
}
