import { ScriptManager } from './src/scripts/script-manager.js';

console.log('🧪 Testing inbound call configuration fix...');

try {
    const scriptManager = new ScriptManager();
    
    // Test current incoming script
    console.log('\n1. Current incoming script:');
    const currentScript = scriptManager.getCurrentIncomingScript();
    console.log(currentScript);
    
    if (currentScript) {
        const config = scriptManager.getScriptConfig(currentScript.id, true);
        console.log('Config:', {
            hasInstructions: !!config?.aiInstructions,
            instructionsLength: config?.aiInstructions?.length || 0,
            voice: config?.voice,
            model: config?.model,
            scriptId: config?.scriptId
        });
    }
    
    // Test default script (ID 7)
    console.log('\n2. Default script (ID 7):');
    const defaultScript = scriptManager.getScriptConfig(7, true);
    console.log('Config:', {
        hasInstructions: !!defaultScript?.aiInstructions,
        instructionsLength: defaultScript?.aiInstructions?.length || 0,
        voice: defaultScript?.voice,
        model: defaultScript?.model,
        scriptId: defaultScript?.scriptId
    });
    
    console.log('\n✅ Test completed');
    
} catch (error) {
    console.error('❌ Error:', error.message);
}
